<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Service\Order;

use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Status\HistoryFactory;
use Magento\Sales\Api\OrderManagementInterface;
use Magento\Framework\Event\ManagerInterface as EventManagerInterface;
use Comave\Sales\Service\Order\RefundService;
use Comave\Sales\Service\Order\NotificationService;
use Psr\Log\LoggerInterface;

/**
 * Order Cancellation Service
 */
class CancellationService
{
    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param HistoryFactory $historyFactory
     * @param OrderManagementInterface $orderManagement
     * @param EventManagerInterface $eventManager
     * @param RefundService $refundService
     * @param NotificationService $notificationService
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly HistoryFactory $historyFactory,
        private readonly OrderManagementInterface $orderManagement,
        private readonly EventManagerInterface $eventManager,
        private readonly RefundService $refundService,
        private readonly NotificationService $notificationService,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Cancel order with specific reason
     *
     * @param Order $order
     * @param string $reason
     * @param string $comment
     * @return void
     * @throws LocalizedException
     */
    public function cancelOrderWithReason(Order $order, string $reason, string $comment = ''): void
    {
        try {
            if (!$order->canCancel()) {
                throw new LocalizedException(__('Order cannot be cancelled.'));
            }

            $this->orderManagement->cancel($order->getEntityId());

            $order->setStatus($reason);
            
            $this->addCancellationComment($order, $reason, $comment);

            $this->orderRepository->save($order);

            $this->processRefundIfNeeded($order);

            $this->notificationService->sendCancellationNotification($order, $reason, $comment);

            $this->eventManager->dispatch(
                'comave_order_cancelled_with_reason',
                [
                    'order' => $order,
                    'reason' => $reason,
                    'comment' => $comment
                ]
            );

            $this->logger->info(
                'Order cancelled successfully',
                [
                    'order_id' => $order->getEntityId(),
                    'increment_id' => $order->getIncrementId(),
                    'reason' => $reason
                ]
            );

        } catch (\Exception $e) {
            $this->logger->error(
                'Failed to cancel order',
                [
                    'order_id' => $order->getEntityId(),
                    'error' => $e->getMessage()
                ]
            );
            throw new LocalizedException(__('Failed to cancel order: %1', $e->getMessage()));
        }
    }

    /**
     * Add cancellation comment to order history
     *
     * @param Order $order
     * @param string $reason
     * @param string $comment
     * @return void
     */
    private function addCancellationComment(Order $order, string $reason, string $comment): void
    {
        $reasonLabel = $reason === 'canceled_by_seller' ? 'Canceled by Seller' : 'Canceled by Customer';
        
        $historyComment = $reasonLabel;
        if (!empty($comment)) {
            $historyComment .= ': ' . $comment;
        }

        $history = $this->historyFactory->create();
        $history->setParentId($order->getEntityId())
            ->setStatus($reason)
            ->setComment($historyComment)
            ->setIsCustomerNotified(true)
            ->setIsVisibleOnFront(true);

        $order->addStatusHistory($history);
    }

    /**
     * Process refund if payment was captured
     *
     * @param Order $order
     * @return void
     */
    private function processRefundIfNeeded(Order $order): void
    {
        try {
            if ($order->hasInvoices() && $order->canCreditmemo()) {
                $this->refundService->processAutomaticRefund($order);
            }
        } catch (\Exception $e) {
            $this->logger->warning(
                'Failed to process automatic refund for cancelled order',
                [
                    'order_id' => $order->getEntityId(),
                    'error' => $e->getMessage()
                ]
            );
        }
    }
}
