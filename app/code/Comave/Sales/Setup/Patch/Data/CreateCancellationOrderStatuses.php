<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Sales\Model\Order\StatusFactory;
use Magento\Sales\Model\ResourceModel\Order\Status as StatusResource;
use Magento\Sales\Model\Order\Status;
use Psr\Log\LoggerInterface;

/**
 * Create custom cancellation order statuses
 */
class CreateCancellationOrderStatuses implements DataPatchInterface
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param StatusFactory $statusFactory
     * @param StatusResource $statusResource
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly StatusFactory $statusFactory,
        private readonly StatusResource $statusResource,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Apply data patch
     *
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $statusesToCreate = [
            [
                'status' => 'canceled_by_seller',
                'label' => 'Canceled by Seller',
                'state' => Status::STATE_CANCELED,
                'is_default' => false,
                'visible_on_front' => true
            ],
            [
                'status' => 'canceled_by_customer',
                'label' => 'Canceled by Customer',
                'state' => Status::STATE_CANCELED,
                'is_default' => false,
                'visible_on_front' => true
            ]
        ];

        foreach ($statusesToCreate as $statusData) {
            try {
                $this->createOrderStatus($statusData);
            } catch (\Exception $e) {
                $this->logger->error(
                    'Failed to create order status: ' . $statusData['status'],
                    ['error' => $e->getMessage()]
                );
            }
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * Create order status if it doesn't exist
     *
     * @param array $statusData
     * @return void
     * @throws \Exception
     */
    private function createOrderStatus(array $statusData): void
    {
        $status = $this->statusFactory->create();
        $status->load($statusData['status']);

        if ($status->getStatus()) {
            // Status already exists, update the label if needed
            if ($status->getLabel() !== $statusData['label']) {
                $status->setLabel($statusData['label']);
                $this->statusResource->save($status);
                $this->logger->info('Updated order status label: ' . $statusData['status']);
            }
            return;
        }

        // Create new status
        $status->setData([
            'status' => $statusData['status'],
            'label' => $statusData['label']
        ]);

        $this->statusResource->save($status);

        // Assign status to state
        $status->assignState(
            $statusData['state'],
            $statusData['is_default'],
            $statusData['visible_on_front']
        );

        $this->logger->info('Created order status: ' . $statusData['status']);
    }

    /**
     * Get dependencies
     *
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * Get aliases
     *
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
