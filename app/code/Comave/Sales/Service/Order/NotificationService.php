<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Service\Order;

use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Sales\Model\Order;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;

/**
 * Order Cancellation Notification Service
 */
class NotificationService
{
    private const string XML_PATH_EMAIL_TEMPLATE = 'sales_email/order_comment/template';
    private const string XML_PATH_EMAIL_IDENTITY = 'sales_email/order_comment/identity';
    private const string XML_PATH_EMAIL_ENABLED = 'sales_email/order_comment/enabled';

    /**
     * @param TransportBuilder $transportBuilder
     * @param StateInterface $inlineTranslation
     * @param StoreManagerInterface $storeManager
     * @param ScopeConfigInterface $scopeConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly TransportBuilder $transportBuilder,
        private readonly StateInterface $inlineTranslation,
        private readonly StoreManagerInterface $storeManager,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Send cancellation notification to customer
     *
     * @param Order $order
     * @param string $reason
     * @param string $comment
     * @return void
     */
    public function sendCancellationNotification(Order $order, string $reason, string $comment = ''): void
    {
        try {
            if (!$this->isEmailEnabled($order->getStoreId())) {
                return;
            }

            $customerEmail = $order->getCustomerEmail();
            if (!$customerEmail) {
                $this->logger->warning('No customer email found for order', ['order_id' => $order->getEntityId()]);
                return;
            }

            $this->inlineTranslation->suspend();

            $reasonLabel = $reason === 'canceled_by_seller' ? 'Canceled by Seller' : 'Canceled by Customer';
            
            $templateVars = [
                'order' => $order,
                'order_id' => $order->getIncrementId(),
                'customer_name' => $order->getCustomerName(),
                'reason' => $reasonLabel,
                'comment' => $comment,
                'store' => $order->getStore()
            ];

            $transport = $this->transportBuilder
                ->setTemplateIdentifier($this->getEmailTemplate($order->getStoreId()))
                ->setTemplateOptions([
                    'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
                    'store' => $order->getStoreId()
                ])
                ->setTemplateVars($templateVars)
                ->setFromByScope($this->getEmailIdentity($order->getStoreId()))
                ->addTo($customerEmail, $order->getCustomerName())
                ->getTransport();

            $transport->sendMessage();

            $this->inlineTranslation->resume();

            $this->logger->info(
                'Cancellation notification sent successfully',
                [
                    'order_id' => $order->getEntityId(),
                    'customer_email' => $customerEmail,
                    'reason' => $reason
                ]
            );

        } catch (\Exception $e) {
            $this->inlineTranslation->resume();
            $this->logger->error(
                'Failed to send cancellation notification',
                [
                    'order_id' => $order->getEntityId(),
                    'error' => $e->getMessage()
                ]
            );
        }
    }

    /**
     * Check if email notifications are enabled
     *
     * @param int $storeId
     * @return bool
     */
    private function isEmailEnabled(int $storeId): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_EMAIL_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get email template
     *
     * @param int $storeId
     * @return string
     */
    private function getEmailTemplate(int $storeId): string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_TEMPLATE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get email identity
     *
     * @param int $storeId
     * @return string
     */
    private function getEmailIdentity(int $storeId): string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EMAIL_IDENTITY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
