<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Comave\Sales\Service\Order\RefundService;
use Magento\Framework\Exception\LocalizedException;

/**
 * Test refund service command
 */
class TestRefundService extends Command
{
    private const string ORDER_ID_ARGUMENT = 'order_id';

    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param RefundService $refundService
     */
    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly RefundService $refundService
    ) {
        parent::__construct();
    }

    /**
     * Configure command
     */
    protected function configure(): void
    {
        $this->setName('comave:test-refund-service')
            ->setDescription('Test refund service for a specific order')
            ->addArgument(
                self::ORDER_ID_ARGUMENT,
                InputArgument::REQUIRED,
                'Order ID to test refund'
            );
    }

    /**
     * Execute command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $orderId = (int) $input->getArgument(self::ORDER_ID_ARGUMENT);
        
        try {
            $order = $this->orderRepository->get($orderId);
            
            $output->writeln("Testing refund for Order ID: {$order->getIncrementId()}");
            $output->writeln("Order Status: {$order->getStatus()}");
            $output->writeln("Order State: {$order->getState()}");
            $output->writeln("Can Credit Memo: " . ($order->canCreditmemo() ? 'Yes' : 'No'));
            $output->writeln("Has Invoices: " . ($order->hasInvoices() ? 'Yes' : 'No'));
            
            if (!$order->canCreditmemo()) {
                $output->writeln("<error>Order cannot be refunded</error>");
                return Command::FAILURE;
            }
            
            $this->refundService->processAutomaticRefund($order);
            $output->writeln("<info>Refund processed successfully!</info>");
            
            return Command::SUCCESS;
            
        } catch (LocalizedException $e) {
            $output->writeln("<error>Error: {$e->getMessage()}</error>");
            return Command::FAILURE;
        } catch (\Exception $e) {
            $output->writeln("<error>Unexpected error: {$e->getMessage()}</error>");
            return Command::FAILURE;
        }
    }
}
